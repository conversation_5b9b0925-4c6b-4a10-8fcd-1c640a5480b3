# 输入Excel与异常警告Excel合并需求

## 对象与路径及对应关系：
- 输入Excel = Test\Input\RPA_List-演示.xlsx
- 异常Excel = Test\Log\ExceptionsLog.xlsx
- 警告Excel = Test\Log\WarningLog.xlsx
- 合并后最终结果Excel = Test\RPA_List-演示-最终结果.xlsx
- 输入Excel中的主列值=异常Excel、警告Excel中的KEY，它们是一一对应关系
- 输入Excel中PRA信息列 = 异常Excel、警告Excel中的EXCEPTION_MSG
- 输入Excel中RPA截图列 = 异常Excel、警告Excel中的EXCEPTION_IMG
- 输入Excel中RPA状态列 = 如果警告Excel中的Key在输入Excel中，则状态为警告，如果异常Excel中的Key在输入Excel中，则状态为异常，如果都没有则状态为已完成。

## 背景：
- RPA运行完后需要将异常和警告整合到输入Excel。每个RPA都用"输入Excel"做为输入参数，运行完RPA后会生成"异常Excel"和"警告Excel"，如果没有异常和警告则不会生成Excel。
- 第二次运行时，可将"合并后最终结果Excel"作为输入Excel，将RPA状态列的"已完成"和"警告"的状态排除掉。
  
## 需求：
- 检测"输入Excel"是否有三列（RPA状态、PRA信息、RPA截图），如果没有则创建，如果有则跳过。
- 将异常Excel、警告Excel中的对应的数据合并到"输入Excel"中。对应不上的全部设置为已完成。
- 将合并后的Excel保存

## 注意：
- 所有的操作都在Excel中的第一个Sheet中操作，多余的sheet不用管。
- 不同RPA的"输入Excel"主列列名都不相同（相当于不重复的主键），当前测试的Excel列表是：编号

## 技术相关：
- 用Python语言
- 方法名：输入Excel合并异常警告Excel
- 参数：输入Excel的主列列名，输入Excel路径，异常Excel和警告Excel的目录，合并后的Excel路径
- 特别说明：异常Excel（ExceptionsLog.xlsx）和警告Excel（WarningLog.xlsx）在同一个目录，需要查找，也可能没有，名字是固定的。合并后的Excel路径如果为空则直接合并到"输入Excel"
- 返回值：无

请实现需求，用MCP读取相关Excel，程序运行结果一定是和"Test\RPA_List-演示-最终结果.xlsx"一样。



还有第二种场景，"合并后最终结果Excel"可以作为"输入Excel"第二次运行。此时合并时不能将原来的警告清除掉。
场景二具体情况有：
"RPA状态"列是"已完成"时，此条数据不会发生变化
"RPA状态"列是"警告"时，此条数据不会发生变化
"RPA状态"列是"异常"时，可能会变为"已完成"或"警告"，也可能还是"异常"。如果是"警告"或"异常"时，需要更新"RPA信息"和"RPA截图"。其它情况下不要更新"RPA信息"和"RPA截图"
"RPA状态"列是""空时，按之前正常逻辑合并。
简单理解就是，如果"RPA状态"列是"已完成"或"警告"时，此条数据不会发生变化。其它情况会根据"异常Excel"和"警告Excel"中的数据来更新"RPA信息"和"RPA截图"


# 删除df中指定状态的数据

## 需求：
- 删除DF中列名"RPA状态"中的值=“已完成" or "警告"

## 技术相关：
- 用Python语言
- 方法名：删除DF中指定状态的数据
- 方法参数：DF
- 返回值：删除后的DF

## 注意
- 要生成测试代码
- 代码写到：RPASystem.Client\RPASystem.ClientWin\ISRPA\PyScript\InputDfDelByRPAStatus.py