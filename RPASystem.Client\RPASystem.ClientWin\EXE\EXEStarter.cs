﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Runtime.CompilerServices;
using Newtonsoft.Json;
using NLog;
using RPASystem.Model;
using RPASystem.Client;

public class EXEStarter
{
    private static readonly ILogger logger = LogManager.GetCurrentClassLogger();

    public readonly static string ExeDirBasePath = "D:\\isearch\\RPAEXE\\";

    /// <summary>
    /// 在指定位置运行EXE，并得到EXE返回值
    /// </summary>
    /// <param name="jobModel">任务模型，包含程序名称、参数等信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>运行结果</returns>
    public static async Task<RunResult> RunAsync(JobModel jobModel, CancellationToken cancellationToken = default)
    {
        var result = new RunResult { IsSucceed = false, Status = 0 };
        string exeDirPath = ExeDirBasePath + jobModel.ProgramName;

        try
        {
            await new PackagesMan(jobModel.ProgramName, exeDirPath, jobModel.ProgramVersion).CheckAndUpdateAsync();
            string exePath = FindExecutablePath(exeDirPath, jobModel.ProgramName);
            string parameter = ConvertToCommandLine(jobModel.Parameter) + " --SubTaskNo " + jobModel.JobTaskName;

            return await ExecuteProcessAsync(exePath, parameter, exeDirPath, jobModel.JobTaskName, cancellationToken);
        }
        catch (OperationCanceledException)
        {
            result.IsSucceed = false;
            result.Status = 3;
            result.ReturnResult = "运行中任务被取消";
            throw;
        }
        catch (Exception ex)
        {
            result.IsSucceed = false;
            result.Status = 0;
            result.ReturnResult = $"运行EXE失败：{ex.Message}";
            logger.Error(ex, $"运行EXE失败：{exeDirPath}");
            //Console.WriteLine(result.ErrorMessage);
            throw;
        }
    }

    /// <summary>
    /// 查找可执行文件路径
    /// </summary>
    private static string FindExecutablePath(string exeDirPath, string programName)
    {
        string exePath = Path.Combine(exeDirPath, programName + ".exe");
        if (File.Exists(exePath))
            return exePath;
        exePath = Path.Combine(exeDirPath, "Main.exe");
        if (File.Exists(exePath))
            return exePath;
        throw new Exception($"找不到EXE的可执行文件！请检查程序包中是否有：{programName}或Main的可执行程序！");
    }

    /// <summary>
    /// 执行进程并等待结果
    /// </summary>
    private static async Task<RunResult> ExecuteProcessAsync(string exePath, string parameter, string workingDirectory, string jobTaskName, CancellationToken cancellationToken)
    {
        ProcessStartInfo psi = new ProcessStartInfo(exePath, parameter)
        {
            UseShellExecute = false,
            WorkingDirectory = workingDirectory,
            CreateNoWindow = false,
        };

        using (Process process = new Process { StartInfo = psi })
        {
            bool isProcessKilled = false;
            using (cancellationToken.Register(() =>
            {
                try
                {
                    if (!process.HasExited) { isProcessKilled = true; process.Kill(); Console.WriteLine($"进程 {process.Id} 已被终止"); }
                }
                catch (Exception ex) { logger.Error(ex, "终止进程失败"); }
            }))
            {
                process.Start();
                Console.WriteLine($"已启动EXE进程，ID: {process.Id}");

                try
                {
                    await Task.Run(() => process.WaitForExit(), cancellationToken);
                    if (isProcessKilled || cancellationToken.IsCancellationRequested) throw new OperationCanceledException(cancellationToken);

                    return GetExeReturn(process, jobTaskName);
                }
                catch (OperationCanceledException) { throw; }
            }
        }
    }

    /// <summary>
    /// Json字符串转CommandLine参数，如：{"aaa":"111"} 转成 --aaa "111"
    /// </summary>
    /// <param name="json">json只接收为空或Json格式字符串</param>
    /// <returns>返回解析成CommandLine参数</returns>
    public static string ConvertToCommandLine(string json)
    {
        if (string.IsNullOrEmpty(json))
        {
            return string.Empty;
        }

        try
        {
            var jsonObject = JsonConvert.DeserializeObject<Dictionary<string, object>>(json);
            var commandLineArgs = new List<string>();

            foreach (var kvp in jsonObject)
            {
                string value;
                // 如果值是对象或数组，保持JSON格式
                if (kvp.Value is Newtonsoft.Json.Linq.JObject || kvp.Value is Newtonsoft.Json.Linq.JArray)
                {
                    value = JsonConvert.SerializeObject(kvp.Value);
                    // 对于复杂对象，使用JSON转义
                    using (var stringWriter = new StringWriter())
                    using (var jsonWriter = new JsonTextWriter(stringWriter) { QuoteChar = '"' })
                    {
                        jsonWriter.WriteValue(value);
                        string escapedValue = stringWriter.ToString();
                        
                        if (escapedValue.StartsWith("\"") && escapedValue.EndsWith("\""))
                        {
                            escapedValue = escapedValue.Substring(1, escapedValue.Length - 2);
                        }
                        
                        commandLineArgs.Add($"--{kvp.Key} \"{escapedValue}\"");
                    }
                }
                else
                {
                    value = kvp.Value?.ToString() ?? string.Empty;
                    // 对于简单字符串值，直接使用，避免过度转义
                    commandLineArgs.Add($"--{kvp.Key} \"{value}\"");
                }
            }

            return string.Join(" ", commandLineArgs);
        }
        catch (JsonException ex)
        {
            Console.WriteLine($"输入的JSON格式不正确: {ex.Message}");
            return string.Empty;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理命令行参数时出错: {ex.Message}");
            return string.Empty;
        }
    }

    /// <summary>
    /// 获取EXE的返回值
    /// </summary>
    private static RunResult GetExeReturn(Process process, string jobTaskName)
    {
        // 检查进程ID返回文件
        string returnFilePath = Path.Combine(process.StartInfo.WorkingDirectory, "ReturnVal", $"{process.Id}.TXT");
        // 老版返回
        if (File.Exists(returnFilePath))
        {
            var retContent = File.ReadAllText(returnFilePath).Replace("Ret==>", "");
            return JsonConvert.DeserializeObject<RunResult>(retContent);
            //return ParseReturnContent(content);
        }

        var returnDir = Path.Combine(process.StartInfo.WorkingDirectory, "Results", $"{jobTaskName}");
        if (Directory.Exists(returnDir))
        {
            // 检查任务编号返回文件
            returnFilePath = Path.Combine(returnDir, "ReturnResult.json");
            RunResult rr = null;
            if (File.Exists(returnFilePath))
                rr = JsonConvert.DeserializeObject<RunResult>(File.ReadAllText(returnFilePath));
            else
                // 没有找到返回文件
                rr = new RunResult { IsSucceed = false, ReturnResult = "未找到返回值文件", Status = 0 };
            rr.OutputFileLocalPath = returnDir;
            return rr;
        }
        else
        {
            return new RunResult { IsSucceed = false, ReturnResult = "未找到任务结果文件夹", Status = 0 };
        }


    }

    /// <summary>
    /// 解析返回内容
    /// </summary>
    private static RunResult ParseReturnContent(string content)
    {
        try { return JsonConvert.DeserializeObject<RunResult>(content); }
        catch
        {
            try
            {
                var resultOld = JsonConvert.DeserializeObject<RunResultOld>(content);
                return new RunResult { IsSucceed = resultOld.IsSucceed, ReturnResult = resultOld.ReturnResult, Status = 1 };
            }
            catch { return new RunResult { IsSucceed = false, ReturnResult = content, Status = 1 }; }
        }
    }
}
